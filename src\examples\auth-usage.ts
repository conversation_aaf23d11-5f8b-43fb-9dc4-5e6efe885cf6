/**
 * 认证接口使用示例
 * 
 * 本文件展示了如何正确使用类型安全的认证接口
 */

import { fetchLogin, fetchGetUserInfo, fetchRefreshToken } from '@/service/api/auth';

/**
 * 登录示例
 */
export async function loginExample() {
  try {
    // 类型安全的登录参数
    const loginParams: Api.Auth.LoginParams = {
      userName: 'admin',
      password: '123456'
    };

    // 调用登录接口
    const { data: loginToken, error } = await fetchLogin(loginParams);

    if (!error && loginToken) {
      console.log('登录成功:', loginToken);
      
      // loginToken 具有完整的类型信息
      console.log('访问token:', loginToken.token);
      console.log('刷新token:', loginToken.refreshToken);
      
      return loginToken;
    } else {
      console.error('登录失败:', error);
      return null;
    }
  } catch (error) {
    console.error('登录异常:', error);
    return null;
  }
}

/**
 * 获取用户信息示例
 */
export async function getUserInfoExample() {
  try {
    const { data: userInfo, error } = await fetchGetUserInfo();

    if (!error && userInfo) {
      console.log('用户信息:', userInfo);
      
      // userInfo 具有完整的类型信息
      console.log('用户ID:', userInfo.userId);
      console.log('用户名:', userInfo.userName);
      console.log('角色列表:', userInfo.roles);
      console.log('权限按钮:', userInfo.buttons);
      
      return userInfo;
    } else {
      console.error('获取用户信息失败:', error);
      return null;
    }
  } catch (error) {
    console.error('获取用户信息异常:', error);
    return null;
  }
}

/**
 * 刷新token示例
 */
export async function refreshTokenExample(refreshToken: string) {
  try {
    // 类型安全的刷新token参数
    const refreshParams: Api.Auth.RefreshTokenParams = {
      refreshToken
    };

    const { data: newToken, error } = await fetchRefreshToken(refreshParams);

    if (!error && newToken) {
      console.log('token刷新成功:', newToken);
      
      // newToken 具有完整的类型信息
      console.log('新的访问token:', newToken.token);
      console.log('新的刷新token:', newToken.refreshToken);
      
      return newToken;
    } else {
      console.error('token刷新失败:', error);
      return null;
    }
  } catch (error) {
    console.error('token刷新异常:', error);
    return null;
  }
}

/**
 * 完整的登录流程示例
 */
export async function completeLoginFlow(userName: string, password: string) {
  // 1. 登录
  const loginToken = await loginExample();
  if (!loginToken) {
    return false;
  }

  // 2. 获取用户信息
  const userInfo = await getUserInfoExample();
  if (!userInfo) {
    return false;
  }

  // 3. 模拟token过期后的刷新
  setTimeout(async () => {
    const newToken = await refreshTokenExample(loginToken.refreshToken);
    if (newToken) {
      console.log('Token已自动刷新');
    }
  }, 30 * 60 * 1000); // 30分钟后刷新

  return true;
}

/**
 * 类型安全性演示
 */
export function typeSafetyDemo() {
  // ✅ 正确的用法
  const validLoginParams: Api.Auth.LoginParams = {
    userName: 'admin',
    password: '123456'
  };

  // ❌ 错误的用法 - TypeScript会报错
  // const invalidLoginParams: Api.Auth.LoginParams = {
  //   username: 'admin',  // 错误：应该是 userName
  //   pwd: '123456'       // 错误：应该是 password
  // };

  // ✅ 正确的刷新token参数
  const validRefreshParams: Api.Auth.RefreshTokenParams = {
    refreshToken: 'some-refresh-token'
  };

  // ❌ 错误的用法 - TypeScript会报错
  // const invalidRefreshParams: Api.Auth.RefreshTokenParams = {
  //   refresh_token: 'some-refresh-token'  // 错误：应该是 refreshToken
  // };

  console.log('类型安全性检查通过');
}
